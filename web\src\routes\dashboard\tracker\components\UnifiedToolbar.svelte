<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import * as Select from '$lib/components/ui/select';
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
  import { Badge } from '$lib/components/ui/badge';
  import {
    Search,
    Download,
    Upload,
    Archive,
    Trash2,
    MoreHorizontal,
    RefreshCw,
    Columns,
  } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';

  export let data: any[] = [];
  export let searchTerm = '';
  export let selectedItems: Set<string> = new Set();
  export let onBulkAction: (action: string, status?: string) => void = () => {};
  export let onFilterChange: (filters: any) => void = () => {};
  export let showVisibleColumns = false; // Only show for table view

  // Filter state
  let appliedFromDate = '';
  let appliedUntilDate = '';
  let selectedJobType = { value: '', label: 'All Job Types' };
  let selectedStatus = { value: '', label: 'All Statuses' };

  // Export/Import state
  let isExporting = false;
  let isImporting = false;

  $: selectedCount = selectedItems.size;

  // Job types for select
  const jobTypeOptions = [
    { value: '', label: 'All Job Types' },
    { value: 'Full-time', label: 'Full-time' },
    { value: 'Part-time', label: 'Part-time' },
    { value: 'Contract', label: 'Contract' },
    { value: 'Freelance', label: 'Freelance' },
    { value: 'Internship', label: 'Internship' },
  ];

  // Status options for select
  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'Applied', label: 'Applied' },
    { value: 'Interview', label: 'Interview' },
    { value: 'Assessment', label: 'Assessment' },
    { value: 'Offer', label: 'Offer' },
    { value: 'Rejected', label: 'Rejected' },
  ];

  // Status options for bulk update (without "All")
  const bulkStatusOptions = [
    { value: 'Applied', label: 'Applied' },
    { value: 'Interview', label: 'Interview' },
    { value: 'Assessment', label: 'Assessment' },
    { value: 'Offer', label: 'Offer' },
    { value: 'Rejected', label: 'Rejected' },
  ];

  // Reactive filter changes
  $: {
    onFilterChange({
      appliedFromDate,
      appliedUntilDate,
      jobType: selectedJobType?.value || '',
      status: selectedStatus?.value || '',
      searchTerm,
    });
  }

  // Bulk action handlers
  function handleBulkArchive() {
    if (selectedCount === 0) {
      toast.error('Please select applications to archive');
      return;
    }
    onBulkAction('archive');
    toast.success(`Archived ${selectedCount} applications`);
  }

  function handleBulkDelete() {
    if (selectedCount === 0) {
      toast.error('Please select applications to delete');
      return;
    }
    onBulkAction('delete');
    toast.success(`Deleted ${selectedCount} applications`);
  }

  function handleBulkStatusUpdate(status: string) {
    if (selectedCount === 0) {
      toast.error('Please select applications to update');
      return;
    }
    onBulkAction('updateStatus', status);
    toast.success(`Updated ${selectedCount} applications to ${status}`);
  }

  // Export CSV
  async function exportCSV() {
    if (isExporting) return;
    isExporting = true;

    try {
      const headers = [
        'Company',
        'Position',
        'Location',
        'Applied Date',
        'Status',
        'Job Type',
        'Next Action',
      ];
      const csvContent = [
        headers.join(','),
        ...data.map((app) =>
          [
            `"${app.company?.replace(/"/g, '""') || ''}"`,
            `"${app.position?.replace(/"/g, '""') || ''}"`,
            `"${app.location?.replace(/"/g, '""') || ''}"`,
            `"${app.appliedDate?.replace(/"/g, '""') || ''}"`,
            `"${app.status?.replace(/"/g, '""') || ''}"`,
            `"${app.jobType?.replace(/"/g, '""') || ''}"`,
            `"${app.nextAction?.replace(/"/g, '""') || ''}"`,
          ].join(',')
        ),
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', 'job_applications.csv');
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success('CSV exported successfully!');
    } catch (error) {
      toast.error('Failed to export CSV');
    } finally {
      setTimeout(() => {
        isExporting = false;
      }, 500);
    }
  }

  // Import CSV
  function handleImportCSV() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.csv';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement)?.files?.[0];
      if (file) {
        isImporting = true;
        setTimeout(() => {
          isImporting = false;
          toast.success('CSV imported successfully!');
        }, 1000);
      }
    };
    input.click();
  }
</script>

<div class="space-y-4">
  <!-- Top row: Search and filters -->
  <div class="flex items-center justify-between">
    <!-- Left side: Search and filters -->
    <div class="flex items-center gap-2">
      <!-- Search -->
      <div class="relative">
        <Search class="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2" />
        <Input
          type="text"
          placeholder="Search for roles or companies..."
          class="w-64 pl-9"
          bind:value={searchTerm} />
      </div>

      <!-- Applied from date -->
      <Input type="date" placeholder="Applied from" class="w-40" bind:value={appliedFromDate} />

      <!-- Applied until date -->
      <Input type="date" placeholder="Applied until" class="w-40" bind:value={appliedUntilDate} />

      <!-- Job Type Select -->
      <Select.Root bind:selected={selectedJobType}>
        <Select.Trigger class="w-40">
          <Select.Value placeholder="Job Type" />
        </Select.Trigger>
        <Select.Content>
          {#each jobTypeOptions as option}
            <Select.Item value={option.value}>{option.label}</Select.Item>
          {/each}
        </Select.Content>
      </Select.Root>

      <!-- Status Select -->
      <Select.Root bind:selected={selectedStatus}>
        <Select.Trigger class="w-32">
          <Select.Value placeholder="Status" />
        </Select.Trigger>
        <Select.Content>
          {#each statusOptions as option}
            <Select.Item value={option.value}>{option.label}</Select.Item>
          {/each}
        </Select.Content>
      </Select.Root>
    </div>

    <!-- Right side: Actions -->
    <div class="flex items-center gap-2">
      {#if showVisibleColumns}
        <Button variant="outline" size="sm" class="h-8">
          <Columns class="mr-2 h-4 w-4" />
          Columns
        </Button>
      {/if}
    </div>
  </div>

  <!-- Bottom row: Selection info, bulk actions, and export/import -->
  <div class="flex items-center justify-between">
    <!-- Left side: Selection info and bulk actions -->
    <div class="flex items-center gap-2">
      {#if selectedCount > 0}
        <Badge variant="secondary" class="text-sm">
          {selectedCount} Jobs Selected
        </Badge>

        <!-- Bulk Actions Dropdown -->
        <DropdownMenu.Root>
          <DropdownMenu.Trigger>
            <Button variant="outline" size="sm" class="h-8">
              <MoreHorizontal class="mr-2 h-4 w-4" />
              Actions
            </Button>
          </DropdownMenu.Trigger>
          <DropdownMenu.Content align="start">
            <DropdownMenu.Item onclick={handleBulkArchive}>
              <Archive class="mr-2 h-4 w-4" />
              Archive
            </DropdownMenu.Item>
            <DropdownMenu.Item onclick={handleBulkDelete} class="text-destructive">
              <Trash2 class="mr-2 h-4 w-4" />
              Delete
            </DropdownMenu.Item>
            <DropdownMenu.Separator />
            <DropdownMenu.Sub>
              <DropdownMenu.SubTrigger>
                <RefreshCw class="mr-2 h-4 w-4" />
                Update Status
              </DropdownMenu.SubTrigger>
              <DropdownMenu.SubContent>
                {#each bulkStatusOptions as status}
                  <DropdownMenu.Item onclick={() => handleBulkStatusUpdate(status.value)}>
                    {status.label}
                  </DropdownMenu.Item>
                {/each}
              </DropdownMenu.SubContent>
            </DropdownMenu.Sub>
          </DropdownMenu.Content>
        </DropdownMenu.Root>
      {/if}
    </div>

    <!-- Right side: Export/Import -->
    <div class="flex items-center gap-2">
      <Button variant="outline" size="sm" onclick={exportCSV} disabled={isExporting} class="h-8">
        <Download class={`mr-2 h-4 w-4 ${isExporting ? 'animate-pulse' : ''}`} />
        {isExporting ? 'Exporting...' : 'Export CSV'}
      </Button>

      <Button
        variant="outline"
        size="sm"
        onclick={handleImportCSV}
        disabled={isImporting}
        class="h-8">
        <Upload class={`mr-2 h-4 w-4 ${isImporting ? 'animate-pulse' : ''}`} />
        {isImporting ? 'Importing...' : 'Import CSV'}
      </Button>
    </div>
  </div>
</div>
