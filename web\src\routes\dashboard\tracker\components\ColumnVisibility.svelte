<script lang="ts">
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
  import { Button } from '$lib/components/ui/button';
  import { Columns } from 'lucide-svelte';

  export let tableModel: any = null;

  // Define columns that can be toggled
  const hidableColumns = [
    { id: 'company', label: 'Company' },
    { id: 'position', label: 'Position' },
    { id: 'status', label: 'Status' },
    { id: 'jobType', label: 'Job Type' },
    { id: 'location', label: 'Location' },
    { id: 'appliedDate', label: 'Applied Date' },
    { id: 'nextAction', label: 'Next Action' },
  ];

  // Function to toggle column visibility directly on the column
  function toggleColumnVisibility(columnId: string) {
    if (!tableModel) return;

    // Try to find the column
    const column = tableModel.getAllColumns?.()?.find((col: any) => col.id === columnId);

    // If column has toggleVisibility method, use it
    if (column?.toggleVisibility) {
      column.toggleVisibility();
    }
  }

  // Function to check if a column is visible
  function isColumnVisible(columnId: string): boolean {
    if (!tableModel) return true;

    // Try to find the column
    const column = tableModel.getAllColumns?.()?.find((col: any) => col.id === columnId);

    // If column has getIsVisible method, use it
    if (column?.getIsVisible) {
      return column.getIsVisible();
    }

    // Default to visible
    return true;
  }
</script>

<DropdownMenu.Root>
  <DropdownMenu.Trigger>
    <Button variant="outline" size="sm" class="h-8">
      <Columns class="mr-2 h-4 w-4" />
      Columns
    </Button>
  </DropdownMenu.Trigger>
  <DropdownMenu.Content align="end" class="w-[150px]">
    <DropdownMenu.Label>Toggle columns</DropdownMenu.Label>
    <DropdownMenu.Separator />
    {#each hidableColumns as column}
      <DropdownMenu.Item onclick={() => toggleColumnVisibility(column.id)}>
        <div class="flex items-center">
          <div class="border-primary mr-2 flex h-4 w-4 items-center justify-center rounded border">
            {#if isColumnVisible(column.id)}
              <div class="bg-primary h-2 w-2 rounded-sm"></div>
            {/if}
          </div>
          <span>{column.label}</span>
        </div>
      </DropdownMenu.Item>
    {/each}
  </DropdownMenu.Content>
</DropdownMenu.Root>
