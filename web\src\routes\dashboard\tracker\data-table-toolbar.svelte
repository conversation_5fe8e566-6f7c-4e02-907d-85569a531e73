<script lang="ts">
  import DataTableViewOptions from './data-table-view-options.svelte';
  import DataTableFacetedFilter from './data-table-faceted-filter.svelte';
  import { statuses, jobTypes, locations } from './types';
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
  import { Badge } from '$lib/components/ui/badge';
  import {
    Search,
    Download,
    Upload,
    Archive,
    Trash2,
    MoreHorizontal,
    RefreshCw,
  } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';

  export let tableModel: any;
  export let data: any[] = [];
  export let searchTerm = '';

  // Local state for UI
  let statusFilter: string[] = [];
  let jobTypeFilter: string[] = [];
  let locationFilter: string[] = [];
  let isExporting = false;
  let isImporting = false;

  // Format status types for the filter
  const statusOptions = statuses.map((status) => ({
    value: status.id,
    label: status.name,
    icon: status.icon,
  }));

  // Format job types for the filter
  const jobTypeOptions = jobTypes.map((type) => ({
    value: type.id,
    label: type.name,
    icon: type.icon,
  }));

  // Format location types for the filter
  const locationOptions = locations.map((location) => ({
    value: location.id,
    label: location.name,
    icon: location.icon,
  }));

  // Count applications by status
  $: statusCounts = statusOptions.reduce((acc, status) => {
    acc[status.value] = data.filter((app) => app.status === status.value).length;
    return acc;
  }, {});

  // Count applications by job type
  $: jobTypeCounts = jobTypeOptions.reduce((acc, type) => {
    acc[type.value] = data.filter((app) => app.jobType === type.value).length;
    return acc;
  }, {});

  // Count applications by location
  $: locationCounts = locationOptions.reduce((acc, location) => {
    acc[location.value] = data.filter((app) => app.location.includes(location.value)).length;
    return acc;
  }, {});

  // Handle status filter changes
  function handleStatusFilterChange(values: string[]) {
    statusFilter = values;
    if (tableModel) {
      if (statusFilter.length > 0) {
        tableModel.getColumn('status')?.setFilterValue(statusFilter);
      } else {
        tableModel.getColumn('status')?.setFilterValue(undefined);
      }
    }
  }

  // Handle job type filter changes
  function handleJobTypeFilterChange(values: string[]) {
    jobTypeFilter = values;
    if (tableModel) {
      if (jobTypeFilter.length > 0) {
        tableModel.getColumn('jobType')?.setFilterValue(jobTypeFilter);
      } else {
        tableModel.getColumn('jobType')?.setFilterValue(undefined);
      }
    }
  }

  // Handle location filter changes
  function handleLocationFilterChange(values: string[]) {
    locationFilter = values;
    if (tableModel) {
      if (locationFilter.length > 0) {
        tableModel.getColumn('location')?.setFilterValue(locationFilter);
      } else {
        tableModel.getColumn('location')?.setFilterValue(undefined);
      }
    }
  }

  // Get selected rows
  $: selectedRows = tableModel ? Object.keys(tableModel.getState().rowSelection || {}).length : 0;

  // Bulk actions
  function handleBulkArchive() {
    if (selectedRows === 0) {
      toast.error('Please select applications to archive');
      return;
    }
    toast.success(`Archived ${selectedRows} applications`);
    // Reset selection
    tableModel?.resetRowSelection();
  }

  function handleBulkDelete() {
    if (selectedRows === 0) {
      toast.error('Please select applications to delete');
      return;
    }
    toast.success(`Deleted ${selectedRows} applications`);
    // Reset selection
    tableModel?.resetRowSelection();
  }

  function handleBulkStatusUpdate(status: string) {
    if (selectedRows === 0) {
      toast.error('Please select applications to update');
      return;
    }
    toast.success(`Updated ${selectedRows} applications to ${status}`);
    // Reset selection
    tableModel?.resetRowSelection();
  }

  // Export CSV
  async function exportCSV() {
    if (isExporting) return;
    isExporting = true;

    try {
      const headers = [
        'Company',
        'Position',
        'Location',
        'Applied Date',
        'Status',
        'Job Type',
        'Next Action',
      ];
      const csvContent = [
        headers.join(','),
        ...data.map((app) =>
          [
            `"${app.company?.replace(/"/g, '""') || ''}"`,
            `"${app.position?.replace(/"/g, '""') || ''}"`,
            `"${app.location?.replace(/"/g, '""') || ''}"`,
            `"${app.appliedDate?.replace(/"/g, '""') || ''}"`,
            `"${app.status?.replace(/"/g, '""') || ''}"`,
            `"${app.jobType?.replace(/"/g, '""') || ''}"`,
            `"${app.nextAction?.replace(/"/g, '""') || ''}"`,
          ].join(',')
        ),
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', 'job_applications.csv');
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success('CSV exported successfully!');
    } catch (error) {
      toast.error('Failed to export CSV');
    } finally {
      setTimeout(() => {
        isExporting = false;
      }, 500);
    }
  }

  // Import CSV
  function handleImportCSV() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.csv';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement)?.files?.[0];
      if (file) {
        isImporting = true;
        // Here you would handle the CSV import
        setTimeout(() => {
          isImporting = false;
          toast.success('CSV imported successfully!');
        }, 1000);
      }
    };
    input.click();
  }
</script>

<div class="space-y-4">
  <!-- Top row: Search, filters, and actions -->
  <div class="flex items-center justify-between">
    <!-- Left side: Search and filters -->
    <div class="flex items-center gap-2">
      <!-- Search -->
      <div class="relative">
        <Search class="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2" />
        <Input
          type="text"
          placeholder="Search for roles or companies..."
          class="w-64 pl-9"
          bind:value={searchTerm} />
      </div>

      <!-- Filters -->
      <div class="flex items-center gap-2">
        <!-- Applied from -->
        <Input type="date" placeholder="Applied from" class="w-40" />

        <!-- Applied until -->
        <Input type="date" placeholder="Applied until" class="w-40" />

        <!-- Job Type Filter -->
        <DataTableFacetedFilter
          title="Job Type"
          options={jobTypeOptions}
          filterValues={jobTypeFilter}
          counts={jobTypeCounts}
          onFilterChange={handleJobTypeFilterChange} />

        <!-- Status Filter -->
        <DataTableFacetedFilter
          title="Status"
          options={statusOptions}
          filterValues={statusFilter}
          counts={statusCounts}
          onFilterChange={handleStatusFilterChange} />
      </div>
    </div>

    <!-- Right side: Actions -->
    <div class="flex items-center gap-2">
      <!-- Visible Columns -->
      {#if tableModel}
        <DataTableViewOptions {tableModel} />
      {/if}
    </div>
  </div>

  <!-- Bottom row: Bulk actions and export/import -->
  <div class="flex items-center justify-between">
    <!-- Left side: Selection info and bulk actions -->
    <div class="flex items-center gap-2">
      {#if selectedRows > 0}
        <Badge variant="secondary" class="text-sm">
          {selectedRows} Jobs Selected
        </Badge>

        <!-- Bulk Actions Dropdown -->
        <DropdownMenu.Root>
          <DropdownMenu.Trigger>
            <Button variant="outline" size="sm" class="h-8">
              <MoreHorizontal class="mr-2 h-4 w-4" />
              Actions
            </Button>
          </DropdownMenu.Trigger>
          <DropdownMenu.Content align="start">
            <DropdownMenu.Item onclick={handleBulkArchive}>
              <Archive class="mr-2 h-4 w-4" />
              Archive
            </DropdownMenu.Item>
            <DropdownMenu.Item onclick={handleBulkDelete} class="text-destructive">
              <Trash2 class="mr-2 h-4 w-4" />
              Delete
            </DropdownMenu.Item>
            <DropdownMenu.Separator />
            <DropdownMenu.Sub>
              <DropdownMenu.SubTrigger>
                <RefreshCw class="mr-2 h-4 w-4" />
                Update Status
              </DropdownMenu.SubTrigger>
              <DropdownMenu.SubContent>
                {#each statuses as status}
                  <DropdownMenu.Item onclick={() => handleBulkStatusUpdate(status.id)}>
                    {@const IconComponent = status.icon}
                    <IconComponent class="mr-2 h-4 w-4" />
                    {status.name}
                  </DropdownMenu.Item>
                {/each}
              </DropdownMenu.SubContent>
            </DropdownMenu.Sub>
          </DropdownMenu.Content>
        </DropdownMenu.Root>
      {/if}
    </div>

    <!-- Right side: Export/Import -->
    <div class="flex items-center gap-2">
      <Button variant="outline" size="sm" onclick={exportCSV} disabled={isExporting} class="h-8">
        <Download class={`mr-2 h-4 w-4 ${isExporting ? 'animate-pulse' : ''}`} />
        {isExporting ? 'Exporting...' : 'Export CSV'}
      </Button>

      <Button
        variant="outline"
        size="sm"
        onclick={handleImportCSV}
        disabled={isImporting}
        class="h-8">
        <Upload class={`mr-2 h-4 w-4 ${isImporting ? 'animate-pulse' : ''}`} />
        {isImporting ? 'Importing...' : 'Import CSV'}
      </Button>
    </div>
  </div>
</div>
