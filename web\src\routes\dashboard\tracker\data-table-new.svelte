<script lang="ts">
  import { FlexRender, createSvelteTable, renderComponent } from '$lib/components/ui/data-table';
  import {
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
  } from '@tanstack/table-core';

  import DataTableColumnHeader from './data-table-column-header.svelte';
  import DataTableRowActions from './data-table-row-actions.svelte';
  import * as TableUI from '$lib/components/ui/table';
  import { Checkbox } from '$lib/components/ui/checkbox';
  import { statusColors, statusIcons } from './types';

  export let data = [];
  export let openApplicationDetails: (app: any) => void;
  export let isKanbanMode = false;

  // State for the table
  let sorting = [
    // Initialize with default sorting by appliedDate in descending order
    { id: 'appliedDate', desc: true },
  ];
  let columnFilters = [];
  let columnVisibility: Record<string, boolean> = {};
  let rowSelection: Record<string, boolean> = {};
  let pagination = { pageIndex: 0, pageSize: 10 };
  let tableInstance = null;
  // We'll use the table's built-in filtering
  let filteredData;
  $: filteredData = data;

  // Create or update table instance with filtered data
  $: {
    tableInstance = createSvelteTable({
      data: filteredData,
      columns: [
        {
          id: 'select',
          size: 40,
          header: ({ table }) => {
            return renderComponent(Checkbox, {
              checked: table.getIsAllPageRowsSelected(),
              indeterminate: table.getIsSomePageRowsSelected() && !table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value),
              'aria-label': 'Select all',
            });
          },
          cell: ({ row }) => {
            return renderComponent(Checkbox, {
              checked: row.getIsSelected(),
              onCheckedChange: (value) => row.toggleSelected(!!value),
              'aria-label': 'Select row',
            });
          },
          enableSorting: false,
          enableHiding: false,
        },
        {
          accessorKey: 'company',
          header: 'Company',
          id: 'company',
          enableSorting: true,
          size: 150, // Fixed width for the company column
          cell: ({ row }) => row.getValue('company'),
        },
        {
          accessorKey: 'position',
          header: 'Position',
          id: 'position',
          enableSorting: true,
          size: 200, // Fixed width for the position column
          cell: ({ row }) => row.getValue('position'),
        },
        {
          accessorKey: 'location',
          header: 'Location',
          id: 'location',
          enableSorting: true,
          size: 150, // Fixed width for the location column
          cell: ({ row }) => row.getValue('location'),
          filterFn: (row, id, filterValues) => {
            if (!Array.isArray(filterValues) || filterValues.length === 0) return true;
            const location = row.getValue(id) as string;
            return filterValues.some((value) => location?.includes(value));
          },
        },
        {
          accessorKey: 'appliedDate',
          header: 'Applied Date',
          id: 'appliedDate',
          enableSorting: true,
          sortingFn: 'datetime',
          sortDescFirst: true,
          size: 150, // Fixed width for the applied date column
          cell: ({ row }) => {
            const dateStr = row.getValue('appliedDate') as string;
            if (!dateStr) return 'N/A';

            const date = new Date(dateStr);
            if (isNaN(date.getTime())) return 'Invalid date';

            return new Intl.DateTimeFormat('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric',
            }).format(date);
          },
        },
        {
          accessorKey: 'status',
          header: 'Status',
          id: 'status',
          enableSorting: true,
          size: 120, // Fixed width for the status column
          cell: ({ row }) => row.getValue('status'),
          filterFn: (row, id, filterValues) => {
            if (!Array.isArray(filterValues) || filterValues.length === 0) return true;
            const status = row.getValue(id);
            return filterValues.includes(status);
          },
        },
        {
          accessorKey: 'jobType',
          header: 'Job Type',
          id: 'jobType',
          enableSorting: true,
          size: 120, // Fixed width for the job type column
          cell: ({ row }) => row.getValue('jobType'),
          filterFn: (row, id, filterValues) => {
            if (!Array.isArray(filterValues) || filterValues.length === 0) return true;
            const jobType = row.getValue(id);
            return filterValues.includes(jobType);
          },
        },
        {
          accessorKey: 'nextAction',
          header: 'Next Action',
          id: 'nextAction',
          enableSorting: true,
          size: 200, // Fixed width for the next action column
          cell: ({ row }) => row.getValue('nextAction') || 'None',
        },
        {
          id: 'actions',
          size: 60, // Fixed width for the actions column
          cell: ({ row }) => {
            return renderComponent(DataTableRowActions, {
              row: row.original,
              openApplicationDetails,
            });
          },
        },
      ],
      state: {
        sorting,
        columnFilters,
        rowSelection,
        pagination,
        columnVisibility,
      },
      onSortingChange: (updater) => {
        sorting = typeof updater === 'function' ? updater(sorting) : updater;
      },
      onColumnFiltersChange: (updater) => {
        columnFilters = typeof updater === 'function' ? updater(columnFilters) : updater;
      },
      onRowSelectionChange: (updater) => {
        rowSelection = typeof updater === 'function' ? updater(rowSelection) : updater;
      },
      onPaginationChange: (updater) => {
        pagination = typeof updater === 'function' ? updater(pagination) : updater;
      },
      onColumnVisibilityChange: (updater) => {
        columnVisibility = typeof updater === 'function' ? updater(columnVisibility) : updater;
      },
      getCoreRowModel: getCoreRowModel(),
      getFilteredRowModel: getFilteredRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getSortedRowModel: getSortedRowModel(),
      enableRowSelection: true,
    });
  }
</script>

{#if tableInstance !== null}
  <div class="space-y-4">
    {#if !isKanbanMode}
      <div class="overflow-auto rounded-md border">
        <table class="w-full caption-bottom text-sm">
          <TableUI.Header>
            {#each tableInstance.getHeaderGroups() as headerGroup}
              <TableUI.Row>
                {#each headerGroup.headers as header}
                  <TableUI.Head
                    style={header.column.columnDef.size
                      ? `width: ${header.column.columnDef.size}px; max-width: ${header.column.columnDef.size}px;`
                      : ''}>
                    {#if !header.isPlaceholder}
                      {#if header.id !== 'select' && header.id !== 'actions'}
                        <DataTableColumnHeader
                          tableModel={tableInstance}
                          cellId={header.id}
                          children={header.column.columnDef.header}
                          props={{
                            sort: {
                              order: header.column.getIsSorted() || undefined,
                              toggle: () => header.column.toggleSorting(),
                              clear: () => header.column.clearSorting(),
                              disabled: !header.column.getCanSort(),
                            },
                          }} />
                      {:else}
                        <FlexRender
                          content={header.column.columnDef.header}
                          context={header.getContext()} />
                      {/if}
                    {/if}
                  </TableUI.Head>
                {/each}
              </TableUI.Row>
            {/each}
          </TableUI.Header>
          <TableUI.Body>
            {#if tableInstance.getRowModel().rows.length}
              {#each tableInstance.getRowModel().rows as row}
                <TableUI.Row
                  class="hover:bg-muted/50 cursor-pointer"
                  data-state={row.getIsSelected() ? 'selected' : undefined}
                  onclick={() => openApplicationDetails(row.original)}>
                  {#each row.getVisibleCells() as cell}
                    <TableUI.Cell
                      style={cell.column.columnDef.size
                        ? `width: ${cell.column.columnDef.size}px; max-width: ${cell.column.columnDef.size}px;`
                        : ''}>
                      {#if cell.column.id === 'status'}
                        <div class="flex items-center">
                          <span
                            class={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                              statusColors[cell.getValue()] || 'bg-gray-100 text-gray-800'
                            }`}>
                            {#if statusIcons[cell.getValue()]}
                              {@const IconComponent = statusIcons[cell.getValue()]}
                              <IconComponent class="mr-1 h-3 w-3" />
                            {/if}
                            {cell.getValue()}
                          </span>
                        </div>
                      {:else}
                        <FlexRender
                          content={cell.column.columnDef.cell}
                          context={cell.getContext()} />
                      {/if}
                    </TableUI.Cell>
                  {/each}
                </TableUI.Row>
              {/each}
            {:else}
              <TableUI.Row>
                <TableUI.Cell
                  colspan={tableInstance.getAllColumns().length}
                  class="h-24 text-center">
                  No results.
                </TableUI.Cell>
              </TableUI.Row>
            {/if}
          </TableUI.Body>
        </table>
      </div>
    {/if}
  </div>
{:else}
  <div class="flex h-24 items-center justify-center">
    <p>Loading...</p>
  </div>
{/if}
