<script lang="ts">
  import { Badge } from '$lib/components/ui/badge';
  import { Button } from '$lib/components/ui/button';
  import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
    CommandSeparator,
  } from '$lib/components/ui/command';
  import { Popover, PopoverContent, PopoverTrigger } from '$lib/components/ui/popover';
  import { Separator } from '$lib/components/ui/separator';
  import { Check, PlusCircle, X } from 'lucide-svelte';

  interface Props {
    title?: string;
    options?: Array<{ value: string; label: string; icon?: any }>;
    filterValues?: string[];
    counts?: Record<string, number>;
    onFilterChange?: (values: string[]) => void;
  }

  let {
    title = '',
    options = [],
    filterValues = [],
    counts = {},
    onFilterChange,
  }: Props = $props();

  let open = $state(false);

  function handleSelect(value: string) {
    const newValues = filterValues.includes(value)
      ? filterValues.filter((v) => v !== value)
      : [...filterValues, value];

    onFilterChange?.(newValues);
  }

  function handleClear() {
    onFilterChange?.([]);
  }
</script>

<Popover bind:open>
  <PopoverTrigger>
    <Button variant="outline" size="sm" class="h-8 border-dashed" onclick={() => (open = true)}>
      <PlusCircle class="mr-2 h-4 w-4" />
      {title}
      {#if filterValues.length > 0}
        <Separator orientation="vertical" class="mx-2 h-4" />
        <Badge variant="secondary" class="rounded-sm px-1 font-normal lg:hidden">
          {filterValues.length}
        </Badge>
        <div class="hidden space-x-1 lg:flex">
          {#each filterValues as value}
            <Badge variant="secondary" class="rounded-sm px-1 font-normal">
              {options.find((option) => option.value === value)?.label || value}
            </Badge>
          {/each}
        </div>
      {/if}
    </Button>
  </PopoverTrigger>
  <PopoverContent class="w-[200px] p-0" align="start">
    <Command>
      <CommandInput placeholder={title} />
      <CommandList>
        <CommandEmpty>No results found.</CommandEmpty>
        <CommandGroup>
          {#each options as option}
            <CommandItem
              value={option.value}
              onSelect={() => handleSelect(option.value)}
              class="flex items-center justify-between">
              <div class="flex items-center">
                {#if option.icon}
                  {@const IconComponent = option.icon}
                  <IconComponent class="text-muted-foreground mr-2 h-4 w-4" />
                {/if}
                <span>{option.label}</span>
                {#if counts && counts[option.value] !== undefined}
                  <span class="text-muted-foreground ml-2 text-xs">({counts[option.value]})</span>
                {/if}
              </div>
              {#if filterValues.includes(option.value)}
                <Check class="h-4 w-4" />
              {/if}
            </CommandItem>
          {/each}
        </CommandGroup>
        {#if filterValues.length > 0}
          <CommandSeparator />
          <CommandGroup>
            <CommandItem onSelect={handleClear} class="justify-center text-center">
              <X class="mr-2 h-4 w-4" />
              Clear filters
            </CommandItem>
          </CommandGroup>
        {/if}
      </CommandList>
    </Command>
  </PopoverContent>
</Popover>
